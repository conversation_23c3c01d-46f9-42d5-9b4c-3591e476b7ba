# Story 4.1: 成本建模系统

## Status

Draft

## Story

**As a** 卫生经济学家，
**I want** 建立全面的成本计算模型，
**so that** 准确评估筛查策略的经济影响。

## Acceptance Criteria

1. 实现筛查成本配置系统（各种筛查工具的直接成本）
2. 建立治疗成本模型（按癌症分期和治疗方案）
3. 实现间接成本计算（时间成本、交通成本等） ——暂不开发
4. 添加成本通胀调整和年度折现率（3%）功能
5. 创建成本参数的敏感性分析工具
6. 实现成本数据的导入和验证功能

## Tasks / Subtasks

- [ ] 任务1：实现筛查成本配置系统 (AC: 1)

  - [ ] 创建src/modules/economics/screening_costs.py文件
  - [ ] 实现ScreeningCostModel类，管理筛查成本
  - [ ] 添加工具特异性成本配置（FIT、结肠镜、乙状结肠镜等）
  - [ ] 实现成本组件分解（检查费、材料费、人工费、设备折旧）
  - [ ] 创建地区和机构特异性成本调整
  - [ ] 添加筛查成本的批量计算功能
- [ ] 任务2：建立治疗成本模型系统 (AC: 2)

  - [ ] 创建src/modules/economics/treatment_costs.py文件
  - [ ] 实现TreatmentCostModel类，管理治疗成本
  - [ ] 添加癌症分期特异性治疗成本（I-IV期）
  - [ ] 实现治疗方案成本配置（手术、化疗、放疗、靶向治疗）
  - [ ] 创建治疗持续时间和强度建模
  - [ ] 添加并发症和不良反应成本建模
- [ ] 任务3：实现间接成本计算系统 (AC: 3) ——暂不开发

  - [ ] 创建src/modules/economics/indirect_costs.py文件
  - [ ] 实现IndirectCostModel类，计算间接成本
  - [ ] 添加时间成本计算（患者和陪护者时间价值）
  - [ ] 实现交通和住宿成本建模
  - [ ] 创建生产力损失成本计算
  - [ ] 添加家庭护理和社会支持成本
- [ ] 任务4：实现成本调整和折现功能 (AC: 4)

  - [ ] 创建src/modules/economics/cost_adjustments.py文件
  - [ ] 实现CostAdjustmentEngine类，处理成本调整
  - [ ] 添加通胀调整功能（基于消费者价格指数）
  - [ ] 实现年度折现率计算（默认3%，可配置）
  - [ ] 创建成本基准年设置和转换功能
  - [ ] 添加货币单位转换和汇率调整
- [ ] 任务5：创建成本敏感性分析工具 (AC: 5)

  - [ ] 创建src/modules/economics/sensitivity_analysis.py文件
  - [ ] 实现SensitivityAnalyzer类，进行敏感性分析
  - [ ] 添加单因素敏感性分析功能
  - [ ] 实现多因素敏感性分析（蒙特卡洛模拟）
  - [ ] 创建敏感性分析结果可视化
  - [ ] 添加敏感性分析报告生成功能
- [ ] 任务6：实现成本数据导入验证系统 (AC: 6)

  - [ ] 创建data/economic_parameters/目录结构
  - [ ] 设计成本数据文件格式（CSV、Excel、JSON、YAML）
  - [ ] 实现成本数据加载和解析功能
  - [ ] 添加成本数据验证和完整性检查
  - [ ] 创建成本数据更新和版本管理
  - [ ] 实现成本数据导出和备份功能

## Dev Notes

### 成本模型数据结构

```python
from dataclasses import dataclass
from typing import Dict, List, Optional
from enum import Enum
from datetime import datetime

class CostCategory(Enum):
    SCREENING = "screening"
    DIAGNOSTIC = "diagnostic"
    TREATMENT = "treatment"
    FOLLOWUP = "followup"
    INDIRECT = "indirect"
    ADVERSE_EVENTS = "adverse_events"

class CostComponent(Enum):
    PROFESSIONAL_FEE = "professional_fee"      # 专业服务费
    FACILITY_FEE = "facility_fee"             # 机构使用费
    EQUIPMENT_COST = "equipment_cost"         # 设备成本
    MATERIAL_COST = "material_cost"           # 材料成本
    MEDICATION_COST = "medication_cost"       # 药物成本
    TIME_COST = "time_cost"                   # 时间成本
    TRANSPORTATION_COST = "transportation_cost" # 交通成本

@dataclass
class CostItem:
    category: CostCategory
    component: CostComponent
    unit_cost: float
    currency: str = "CNY"
    base_year: int = 2023
    source: str = ""
    confidence_interval: Optional[tuple] = None
```

### 筛查成本配置文件格式

```yaml
# data/economic_parameters/screening_costs_china_2023.yaml
screening_costs:
  version: "2023.1"
  currency: "CNY"
  base_year: 2023
  region: "China"
  
  fit_screening:
    professional_fee: 15.0      # 医生解读费
    facility_fee: 10.0          # 机构费用
    material_cost: 8.0          # 检测试剂盒
    processing_cost: 12.0       # 实验室处理
    total_direct_cost: 45.0
  
    indirect_costs:
      patient_time_hours: 0.5   # 患者时间0.5小时
      hourly_wage_rate: 25.0    # 小时工资率
      transportation_cost: 15.0  # 交通费用
      total_indirect_cost: 27.5
  
  colonoscopy_screening:
    professional_fee: 300.0     # 内镜医生费用
    facility_fee: 200.0         # 内镜室使用费
    equipment_cost: 50.0        # 设备折旧
    medication_cost: 80.0       # 麻醉和准备药物
    material_cost: 30.0         # 一次性用品
    total_direct_cost: 660.0
  
    indirect_costs:
      patient_time_hours: 4.0   # 患者时间4小时（包括准备）
      companion_time_hours: 4.0 # 陪护者时间
      hourly_wage_rate: 25.0
      transportation_cost: 30.0
      accommodation_cost: 0.0   # 通常当日完成
      total_indirect_cost: 230.0
```

### 治疗成本模型设计

```python
class TreatmentCostModel:
    def __init__(self, cost_config: Dict):
        self.cost_config = cost_config
        self.stage_specific_costs = cost_config['treatment_costs_by_stage']
        self.treatment_modality_costs = cost_config['treatment_modality_costs']
  
    def calculate_treatment_cost(
        self, 
        cancer_stage: CancerStage, 
        treatment_duration_months: float,
        complications: List[str] = None
    ) -> float:
        """计算治疗总成本"""
      
        # 基础治疗成本（按分期）
        base_cost = self.stage_specific_costs[cancer_stage.value]
      
        # 治疗持续时间调整
        duration_adjusted_cost = base_cost * (treatment_duration_months / 12.0)
      
        # 并发症成本
        complication_cost = 0.0
        if complications:
            for complication in complications:
                complication_cost += self.cost_config['complication_costs'].get(
                    complication, 0.0
                )
      
        # 总治疗成本
        total_cost = duration_adjusted_cost + complication_cost
      
        return total_cost
  
    def calculate_lifetime_treatment_cost(
        self, 
        individual: Individual, 
        diagnosis_age: int
    ) -> float:
        """计算终生治疗成本"""
      
        stage = individual.cancer_stage
      
        # 初始治疗成本
        initial_cost = self.calculate_treatment_cost(stage, 12.0)  # 第一年
      
        # 后续随访成本
        followup_years = max(0, individual.life_expectancy - diagnosis_age)
        annual_followup_cost = self.cost_config['annual_followup_costs'][stage.value]
        followup_cost = annual_followup_cost * followup_years
      
        # 复发治疗成本（基于复发概率）
        recurrence_prob = self.cost_config['recurrence_probabilities'][stage.value]
        recurrence_cost = (
            recurrence_prob * 
            self.cost_config['recurrence_treatment_costs'][stage.value]
        )
      
        return initial_cost + followup_cost + recurrence_cost
```

### 成本折现和调整

```python
class CostAdjustmentEngine:
    def __init__(self, base_year: int = 2023, discount_rate: float = 0.03):
        self.base_year = base_year
        self.discount_rate = discount_rate
        self.inflation_rates = self._load_inflation_data()
  
    def adjust_for_inflation(
        self, 
        cost: float, 
        from_year: int, 
        to_year: int
    ) -> float:
        """通胀调整"""
        if from_year == to_year:
            return cost
      
        cumulative_inflation = 1.0
        start_year = min(from_year, to_year)
        end_year = max(from_year, to_year)
      
        for year in range(start_year, end_year):
            inflation_rate = self.inflation_rates.get(year, 0.03)  # 默认3%
            cumulative_inflation *= (1 + inflation_rate)
      
        if from_year < to_year:
            return cost * cumulative_inflation
        else:
            return cost / cumulative_inflation
  
    def present_value(
        self, 
        future_cost: float, 
        years_in_future: float
    ) -> float:
        """计算现值"""
        return future_cost / ((1 + self.discount_rate) ** years_in_future)
  
    def net_present_value(
        self, 
        cost_stream: List[tuple]  # [(cost, year), ...]
    ) -> float:
        """计算净现值"""
        npv = 0.0
        for cost, year in cost_stream:
            years_from_base = year - self.base_year
            npv += self.present_value(cost, years_from_base)
        return npv
```

### 敏感性分析实现

```python
class SensitivityAnalyzer:
    def __init__(self, cost_model: 'EconomicModel'):
        self.cost_model = cost_model
        self.base_parameters = cost_model.get_parameters()
  
    def one_way_sensitivity_analysis(
        self, 
        parameter_name: str, 
        variation_range: tuple = (-0.2, 0.2),
        steps: int = 10
    ) -> Dict:
        """单因素敏感性分析"""
      
        base_value = self.base_parameters[parameter_name]
        results = []
      
        # 生成参数变化范围
        min_mult, max_mult = variation_range
        multipliers = np.linspace(1 + min_mult, 1 + max_mult, steps)
      
        for multiplier in multipliers:
            # 调整参数
            adjusted_params = self.base_parameters.copy()
            adjusted_params[parameter_name] = base_value * multiplier
          
            # 重新计算成本
            self.cost_model.update_parameters(adjusted_params)
            result = self.cost_model.calculate_total_cost()
          
            results.append({
                'parameter_value': base_value * multiplier,
                'multiplier': multiplier,
                'total_cost': result['total_cost'],
                'icer': result.get('icer', None)
            })
      
        # 恢复基础参数
        self.cost_model.update_parameters(self.base_parameters)
      
        return {
            'parameter_name': parameter_name,
            'base_value': base_value,
            'results': results
        }
```

### 成本数据验证规则

- 成本值: 必须 ≥ 0
- 货币单位: 必须为有效的ISO货币代码
- 基准年: 1990 ≤ 年份 ≤ 当前年份+5
- 折现率: 0% ≤ 折现率 ≤ 10%
- 置信区间: 下限 ≤ 点估计 ≤ 上限

### Testing

#### 测试文件位置

- `tests/unit/test_screening_costs.py`
- `tests/unit/test_treatment_costs.py`
- `tests/unit/test_cost_adjustments.py`
- `tests/integration/test_cost_modeling.py`

#### 测试标准

- 成本计算准确性测试
- 折现和通胀调整测试
- 敏感性分析功能测试
- 成本数据导入验证测试
- 成本模型集成测试

#### 测试框架和模式

- 使用pytest参数化测试不同成本场景
- Mock外部数据源测试数据加载
- 数值精度测试验证计算准确性
- 边界值测试验证参数范围

#### 特定测试要求

- 成本计算精度: 误差 < 0.01元
- 折现计算准确性: 与标准公式偏差 < 0.1%
- 数据加载时间: < 200ms
- 敏感性分析完整性: 覆盖所有关键参数

## Change Log

| Date       | Version | Description  | Author       |
| ---------- | ------- | ------------ | ------------ |
| 2025-07-31 | 1.0     | 初始故事创建 | Scrum Master |

## Dev Agent Record

*此部分将由开发代理在实施过程中填写*

### Agent Model Used

*待填写*

### Debug Log References

*待填写*

### Completion Notes List

*待填写*

### File List

*待填写*

## QA Results

*此部分将由QA代理在完成故事实施的QA审查后填写*
