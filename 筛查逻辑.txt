      % perhaps we do screening?
        if flag.Screening
            % x-matrix
            % 1: PercentP<PERSON>, 2: Adherence,    3: FollowUp,   4:y-start, 5:y-end,
            % 6: interval,   7: y after colo, 8: specificity
            % y-matrix
            % 1: colonoscopy, 2: Rectosigmoidoscopy, 3: FOBT, 4: I_FOBT
            % 5: Sept9_Hi<PERSON><PERSON>, 6: Sept9_HiSpec, 7: other
            screen = ScreeningPreference ~= 0;
            if any(screen)
                preferences = ScreeningPreference(screen);
                IDs = SubjectIDs(screen);
 
1.年龄要大于等于起始年龄、小于等于终止年龄；
2.满足1，且距离上次诊断性肠镜检查的的年数要 大于等于 year after colo；
3.满足2的基础上，本次与上次的筛查周期要大于等于筛查间隔期，且依从；
4.诊断性肠镜：初筛阳性，  且小于诊断性肠镜依从性。

       
                toScreen = dcurrentYear >= ScreeningTest(preferences,4) & dcurrentYear < ScreeningTest(preferences,5);
                toScreen = toScreen & dcurrentYear-Last.Colonoscopy(IDs)' >= ScreeningTest(preferences,7); % we only screen so and so many years after the last Colonoscopy
                
                if any(toScreen)
                    
                    NtoScreen = sum(toScreen); %number of individuals to screen
                    preferences = preferences(toScreen); %preferences of screened indivisuals
                    IDs = IDs(toScreen); %IDs of the screened subjects
                    
                    compliance = rand(NtoScreen,1) < ScreeningTest(preferences,2); %compliance randomization for each screened subject
                    
                    screenColo = preferences == 1 & compliance & dcurrentYear-Last.Colonoscopy(IDs)' >=  ScreeningTest(preferences,6); % Colonoscopy % corrected 23.04.2018, %interval

 otherTest = preferences > 2 & dcurrentYear-Last.ScreenTest(IDs)' >= ScreeningTest(preferences,6) & compliance;

                        Limit = zeros(Nother,1);
                        for kk = 1:length(IDsSR)
                            MC = max(Cstage(CID == IDsSR(kk)));
                            MP = max(Pstage(PID == IDsSR(kk)));
                            if isempty(MC)
                               MC = -1; 
                            end
                            if isempty(MP)
                               MP = -1; 
                            end
                            M = max(MC, MP);
                            if M > 0 %if anything to screen
                               Limit(kk) = Sensitivity(preferencesOther(kk),M);
                            end
                        end
                        Limit = max(Limit,1-ScreeningTest(preferencesOther,8));
                        
                        positiveAndFollowup = rand(Nother,1) < Limit & rand(Nother,1) < ScreeningTest(preferencesOther, 3);